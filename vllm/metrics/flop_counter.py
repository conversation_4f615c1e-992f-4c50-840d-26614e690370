# SPDX-License-Identifier: Apache-2.0

"""
FLOP (Floating Point Operations) counter for transformer model inference.

This module provides utilities to calculate theoretical FLOPs for transformer models
during inference, helping with performance analysis and GPU utilization debugging.
"""

from typing import Optional

from vllm.config import ModelConfig
from vllm.logger import init_logger

logger = init_logger(__name__)


class FLOPCounter:
    """
    Calculates theoretical FLOPs for transformer model inference.
    
    This class provides simplified FLOP calculation for performance analysis
    and GPU utilization debugging. It focuses on the main computational
    components: attention and MLP layers.
    """
    
    def __init__(self, model_config: ModelConfig):
        """
        Initialize FLOP counter with model configuration.
        
        Args:
            model_config: Model configuration containing architecture details
        """
        self.model_config = model_config
        
        # Extract key model parameters
        hf_config = model_config.hf_config
        self.num_layers = getattr(hf_config, 'num_hidden_layers', 
                                 getattr(hf_config, 'num_layers', 12))
        self.hidden_size = getattr(hf_config, 'hidden_size', 768)
        self.num_attention_heads = getattr(hf_config, 'num_attention_heads', 12)
        self.intermediate_size = getattr(hf_config, 'intermediate_size', 
                                       getattr(hf_config, 'ffn_dim', 
                                              self.hidden_size * 4))
        
        logger.info(f"FLOPCounter initialized for model with {self.num_layers} layers, "
                   f"{self.hidden_size} hidden size, {self.num_attention_heads} attention heads")
    
    def count_total_flops(self, batch_size: int, num_prompt_tokens: int, 
                         num_decode_tokens: int, kv_cache_len: int = 0) -> int:
        """
        Calculate total FLOPs for a forward pass.
        
        Args:
            batch_size: Number of sequences in the batch
            num_prompt_tokens: Number of prompt tokens (for prefill)
            num_decode_tokens: Number of decode tokens
            kv_cache_len: Length of KV cache
            
        Returns:
            Total number of FLOPs
        """
        total_flops = 0
        
        # Prefill phase
        if num_prompt_tokens > 0:
            # Simplified FLOP calculation for prefill
            # Attention: batch_size * seq_len^2 * hidden_size * num_layers
            attention_flops = batch_size * num_prompt_tokens * num_prompt_tokens * self.hidden_size * self.num_layers
            
            # MLP: batch_size * seq_len * hidden_size * intermediate_size * 2 * num_layers
            mlp_flops = batch_size * num_prompt_tokens * self.hidden_size * self.intermediate_size * 2 * self.num_layers
            
            total_flops += attention_flops + mlp_flops
        
        # Decode phase
        if num_decode_tokens > 0:
            # For decode, attention is over the full KV cache
            total_seq_len = kv_cache_len + num_prompt_tokens + num_decode_tokens
            
            # Attention: batch_size * decode_tokens * total_seq_len * hidden_size * num_layers
            attention_flops = batch_size * num_decode_tokens * total_seq_len * self.hidden_size * self.num_layers
            
            # MLP: batch_size * decode_tokens * hidden_size * intermediate_size * 2 * num_layers
            mlp_flops = batch_size * num_decode_tokens * self.hidden_size * self.intermediate_size * 2 * self.num_layers
            
            total_flops += attention_flops + mlp_flops
        
        return total_flops


def create_flop_counter(model_config: ModelConfig) -> Optional[FLOPCounter]:
    """
    Create a FLOP counter for the given model configuration.
    
    Args:
        model_config: Model configuration
        
    Returns:
        FLOPCounter instance if successful, None otherwise
    """
    try:
        return FLOPCounter(model_config)
    except Exception as e:
        logger.warning(f"Failed to create FLOP counter: {e}")
        return None
