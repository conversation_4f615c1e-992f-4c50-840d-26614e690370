#!/usr/bin/env python3
"""
Test script to verify that the type processing logic works correctly
for nested Optional[Literal[...]] types after the SkipValidation fix.
"""

import sys
import os
from typing import Literal, Optional, Union, get_args, get_origin

# Add the vllm directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import only the specific functions we need to test
try:
    from vllm.engine.arg_utils import contains_type, get_type, literal_to_kwargs
    IMPORT_SUCCESS = True
except ImportError as e:
    print(f"Warning: Could not import from vllm: {e}")
    print("Will test with standalone implementation")
    IMPORT_SUCCESS = False

# Define test types similar to what we have in the real code
QuantizationMethods = Literal["aqlm", "awq", "gptq", "fp8", "marlin"]

# Standalone implementation for testing if imports fail
def standalone_contains_type(type_hints: set, target_type) -> bool:
    """Check if the type hints contain a specific type."""
    for type_hint in type_hints:
        if type_hint is target_type or get_origin(type_hint) is target_type:
            return True
        # Recursively check for the type within Union/Optional types
        if get_origin(type_hint) in {Union}:
            nested_args = get_args(type_hint)
            if any((arg is target_type or get_origin(arg) is target_type) for arg in nested_args):
                return True
            # Recursively check nested Union/Optional types
            if any(standalone_contains_type({arg}, target_type) for arg in nested_args
                   if get_origin(arg) in {Union}):
                return True
    return False

def standalone_get_type(type_hints: set, target_type):
    """Get the specific type from the type hints."""
    for type_hint in type_hints:
        if type_hint is target_type or get_origin(type_hint) is target_type:
            return type_hint
        # Recursively search for the type within Union/Optional types
        if get_origin(type_hint) in {Union}:
            nested_args = get_args(type_hint)
            for arg in nested_args:
                if arg is target_type or get_origin(arg) is target_type:
                    return arg
                # Recursively search nested Union/Optional types
                if get_origin(arg) in {Union}:
                    result = standalone_get_type({arg}, target_type)
                    if result is not None:
                        return result
    return None

def test_type_processing():
    """Test that type processing works correctly for nested Optional[Literal[...]] types."""
    print("Testing type processing logic...")

    # Use imported functions if available, otherwise use standalone
    if IMPORT_SUCCESS:
        contains_type_func = contains_type
        get_type_func = get_type
        print("Using imported functions from vllm")
    else:
        contains_type_func = standalone_contains_type
        get_type_func = standalone_get_type
        print("Using standalone functions")

    # Test case 1: Direct Literal type
    print("\n1. Testing direct Literal type:")
    type_hints_1 = {QuantizationMethods}
    result_1 = contains_type_func(type_hints_1, Literal)
    print(f"   Type hints: {type_hints_1}")
    print(f"   Contains Literal: {result_1}")
    assert result_1 == True, "Should find Literal in direct Literal type"

    # Test case 2: Optional[Literal[...]] type
    print("\n2. Testing Optional[Literal[...]] type:")
    type_hints_2 = {Optional[QuantizationMethods]}
    result_2 = contains_type_func(type_hints_2, Literal)
    print(f"   Type hints: {type_hints_2}")
    print(f"   Contains Literal: {result_2}")
    assert result_2 == True, "Should find Literal in Optional[Literal[...]] type"

    # Test case 3: Get the Literal type from Optional[Literal[...]]
    print("\n3. Testing get_type for Optional[Literal[...]]:")
    literal_type = get_type_func(type_hints_2, Literal)
    print(f"   Type hints: {type_hints_2}")
    print(f"   Found Literal type: {literal_type}")
    assert literal_type is not None, "Should find the Literal type"

    # Test case 4: Extract choices from the found Literal type
    print("\n4. Testing choice extraction:")
    if literal_type is not None:
        choices = get_args(literal_type)
        print(f"   Literal type: {literal_type}")
        print(f"   Extracted choices: {choices}")
        expected_choices = ("aqlm", "awq", "gptq", "fp8", "marlin")
        assert choices == expected_choices, f"Choices should be {expected_choices}, got {choices}"

    print("\n✅ SUCCESS: All type processing tests passed!")
    return True

def test_cli_parser():
    """Test that the CLI parser can be created without errors."""
    print("\nTesting CLI parser creation...")

    try:
        from vllm.engine.arg_utils import EngineArgs
        from vllm.utils import FlexibleArgumentParser

        parser = FlexibleArgumentParser()
        parser = EngineArgs.add_cli_args(parser)

        # Try to get the quantization argument
        quantization_action = None
        for action in parser._actions:
            if hasattr(action, 'dest') and action.dest == 'quantization':
                quantization_action = action
                break

        if quantization_action is None:
            print("❌ ERROR: quantization argument not found in parser")
            return False

        print(f"Quantization action choices: {quantization_action.choices}")

        if quantization_action.choices is None:
            print("❌ ERROR: quantization action has no choices")
            return False

        print("✅ SUCCESS: CLI parser created successfully with quantization choices!")
        return True

    except Exception as e:
        print(f"❌ ERROR: Failed to create CLI parser: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Testing quantization CLI choice setup after SkipValidation fix")
    print("=" * 60)

    success1 = test_quantization_choices()
    success2 = test_cli_parser()

    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED! The fix is working correctly.")
        sys.exit(0)
    else:
        print("💥 SOME TESTS FAILED! The fix needs more work.")
        sys.exit(1)
