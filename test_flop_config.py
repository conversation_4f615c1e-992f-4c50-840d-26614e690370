#!/usr/bin/env python3
"""
Test script to verify that the FLOP counting configuration is properly implemented.
"""

import sys
import os

# Add the vllm directory to the path
sys.path.insert(0, '/Users/<USER>/py-workspace/github.com/lengrongfu/vllm')

def test_config_import():
    """Test that we can import the config classes."""
    try:
        from vllm.config import ObservabilityConfig
        print("✓ Successfully imported ObservabilityConfig")
        
        # Test that collect_flop_stats field exists
        config = ObservabilityConfig()
        print(f"✓ Default collect_flop_stats value: {config.collect_flop_stats}")
        
        # Test setting the field
        config = ObservabilityConfig(collect_flop_stats=True)
        print(f"✓ Set collect_flop_stats to True: {config.collect_flop_stats}")
        
        return True
    except Exception as e:
        print(f"✗ Failed to import or use ObservabilityConfig: {e}")
        return False

def test_engine_args():
    """Test that EngineArgs includes the collect_flop_stats field."""
    try:
        from vllm.engine.arg_utils import EngineArgs
        print("✓ Successfully imported EngineArgs")
        
        # Test that collect_flop_stats field exists
        args = EngineArgs()
        print(f"✓ Default collect_flop_stats value: {args.collect_flop_stats}")
        
        # Test setting the field
        args = EngineArgs(collect_flop_stats=True)
        print(f"✓ Set collect_flop_stats to True: {args.collect_flop_stats}")
        
        return True
    except Exception as e:
        print(f"✗ Failed to import or use EngineArgs: {e}")
        return False

def test_flop_counter():
    """Test that we can import the FLOP counter."""
    try:
        from vllm.metrics.flop_counter import FLOPCounter, create_flop_counter
        print("✓ Successfully imported FLOPCounter and create_flop_counter")
        return True
    except Exception as e:
        print(f"✗ Failed to import FLOP counter: {e}")
        return False

def test_flop_stats():
    """Test that we can import the FLOP stats."""
    try:
        from vllm.v1.metrics.stats import FLOPStats
        print("✓ Successfully imported FLOPStats")
        
        # Test creating FLOPStats
        stats = FLOPStats()
        print(f"✓ Created FLOPStats: total_flops={stats.total_flops}, computation_time={stats.computation_time}")
        
        # Test FLOPs per second calculation
        stats.total_flops = 1000000
        stats.computation_time = 0.001
        flops_per_second = stats.get_flops_per_second()
        print(f"✓ FLOPs per second calculation: {flops_per_second:.2e}")
        
        return True
    except Exception as e:
        print(f"✗ Failed to import or use FLOPStats: {e}")
        return False

def test_command_line_parsing():
    """Test that command line parsing works with --collect-flop-stats."""
    try:
        from vllm.engine.arg_utils import EngineArgs
        import argparse
        
        # Create a parser
        parser = EngineArgs.add_cli_args(argparse.ArgumentParser())
        
        # Test parsing with --collect-flop-stats
        args = parser.parse_args(['--model', 'facebook/opt-125m', '--collect-flop-stats'])
        print(f"✓ Parsed --collect-flop-stats: {args.collect_flop_stats}")
        
        # Test creating EngineArgs from parsed args
        engine_args = EngineArgs.from_cli_args(args)
        print(f"✓ EngineArgs from CLI: collect_flop_stats={engine_args.collect_flop_stats}")
        
        return True
    except Exception as e:
        print(f"✗ Failed command line parsing test: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing FLOP counting configuration implementation...")
    print("=" * 60)
    
    tests = [
        ("Config Import", test_config_import),
        ("Engine Args", test_engine_args),
        ("FLOP Counter", test_flop_counter),
        ("FLOP Stats", test_flop_stats),
        ("Command Line Parsing", test_command_line_parsing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! FLOP counting configuration is properly implemented.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
